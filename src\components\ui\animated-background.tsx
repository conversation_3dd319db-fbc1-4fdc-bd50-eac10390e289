'use client'

import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'

interface AnimatedBackgroundProps {
  variant?: 'default' | 'mesh-1' | 'mesh-2' | 'gradient-shift' | 'hero' | 'particles' | 'minimal'
  intensity?: 'low' | 'medium' | 'high'
  className?: string
  children?: React.ReactNode
}

export function AnimatedBackground({
  variant = 'default',
  intensity = 'medium',
  className,
  children
}: AnimatedBackgroundProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    // Check for mobile
    const checkMobile = () => setIsMobile(window.innerWidth < 768)
    checkMobile()

    const handleResize = () => checkMobile()
    window.addEventListener('resize', handleResize)

    return () => window.removeEventListener('resize', handleResize)
  }, [])
  const getBackgroundClass = () => {
    switch (variant) {
      case 'mesh-1':
        return 'bg-mesh-1'
      case 'mesh-2':
        return 'bg-mesh-2'
      case 'gradient-shift':
        return 'animate-gradient-shift'
      case 'hero':
        return 'bg-gradient-hero-enhanced'
      case 'particles':
        return 'bg-section-light'
      case 'minimal':
        return 'bg-section-accent'
      default:
        return 'bg-page'
    }
  }

  const getParticleCount = () => {
    const baseCount = (() => {
      switch (intensity) {
        case 'low':
          return 3
        case 'high':
          return 8
        default:
          return 5
      }
    })()

    // Reduce particles on mobile or if motion is reduced
    if (isMobile || prefersReducedMotion) {
      return prefersReducedMotion ? 0 : Math.max(2, Math.floor(baseCount * 0.6))
    }

    return baseCount
  }

  const getOpacity = () => {
    switch (intensity) {
      case 'low':
        return 'opacity-20'
      case 'high':
        return 'opacity-40'
      default:
        return 'opacity-30'
    }
  }

  const particleCount = getParticleCount()
  const particles = Array.from({ length: particleCount }, (_, i) => i)

  return (
    <div className={cn('relative overflow-hidden', getBackgroundClass(), className)}>
      {/* Animated particles */}
      {variant !== 'minimal' && (
        <div className={cn('absolute inset-0', getOpacity())}>
          {particles.map((i) => (
            <div
              key={i}
              className={cn(
                'absolute rounded-full',
                i % 3 === 0 ? 'bg-gradient-to-br from-primary/20 to-transparent' :
                i % 3 === 1 ? 'bg-gradient-to-br from-secondary/20 to-transparent' :
                'bg-gradient-to-br from-accent/20 to-transparent',
                i % 4 === 0 ? 'w-32 h-32 animate-morph' :
                i % 4 === 1 ? 'w-24 h-24 animate-particle-float' :
                i % 4 === 2 ? 'w-20 h-20 animate-subtle-pulse' :
                'w-16 h-16 animate-float'
              )}
              style={{
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                animationDelay: `${i * 0.8}s`,
              }}
            />
          ))}
        </div>
      )}

      {/* Gradient overlays for depth */}
      {variant !== 'minimal' && (
        <>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-background/5 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-background/3 to-transparent" />
        </>
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

// Specialized variants for common use cases
export function HeroAnimatedBackground({ children, className }: { children?: React.ReactNode; className?: string }) {
  return (
    <AnimatedBackground variant="hero" intensity="high" className={className}>
      {children}
    </AnimatedBackground>
  )
}

export function SectionAnimatedBackground({ children, className }: { children?: React.ReactNode; className?: string }) {
  return (
    <AnimatedBackground variant="mesh-2" intensity="low" className={className}>
      {children}
    </AnimatedBackground>
  )
}

export function ShopAnimatedBackground({ children, className }: { children?: React.ReactNode; className?: string }) {
  return (
    <AnimatedBackground variant="particles" intensity="medium" className={className}>
      {children}
    </AnimatedBackground>
  )
}

export function MinimalAnimatedBackground({ children, className }: { children?: React.ReactNode; className?: string }) {
  return (
    <AnimatedBackground variant="minimal" intensity="low" className={className}>
      {children}
    </AnimatedBackground>
  )
}
