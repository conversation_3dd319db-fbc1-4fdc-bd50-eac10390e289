@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --card: 158 5% 99%;
  --card-foreground: 158 15% 15%;
  --popover: 158 5% 99%;
  --popover-foreground: 158 15% 15%;
  --primary: 158 64% 25%;
  --primary-foreground: 45 25% 97%;
  --secondary: 45 85% 65%;
  --secondary-foreground: 158 64% 15%;
  --muted: 158 10% 95%;
  --muted-foreground: 158 8% 45%;
  --accent: 158 25% 85%;
  --accent-foreground: 158 64% 20%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --border: 158 15% 90%;
  --input: 158 15% 92%;
  --ring: 158 64% 25%;
  --chart-1: 158 64% 25%;
  --chart-2: 45 85% 65%;
  --chart-3: 158 25% 85%;
  --chart-4: 158 40% 35%;
  --chart-5: 45 70% 55%;
  --radius: 0.625rem;

  /* Enhanced Visual Variables */
  --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--accent)) 100%);
  --gradient-hero: linear-gradient(135deg, hsl(var(--primary) / 0.08) 0%, hsl(var(--background)) 20%, hsl(var(--secondary) / 0.06) 60%, hsl(var(--background)) 100%),
                   radial-gradient(circle at 30% 20%, hsl(var(--primary) / 0.12) 0%, transparent 50%),
                   radial-gradient(circle at 70% 80%, hsl(var(--secondary) / 0.10) 0%, transparent 50%);
  --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--primary) / 0.08) 100%);
  --gradient-luxury: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);

  /* Advanced Background Gradients */
  --gradient-mesh-1: radial-gradient(circle at 20% 80%, hsl(var(--primary) / 0.12) 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, hsl(var(--secondary) / 0.15) 0%, transparent 50%),
                     radial-gradient(circle at 40% 40%, hsl(var(--accent) / 0.1) 0%, transparent 50%);
  --gradient-mesh-2: radial-gradient(circle at 60% 30%, hsl(var(--primary) / 0.08) 0%, transparent 60%),
                     radial-gradient(circle at 30% 70%, hsl(var(--secondary) / 0.12) 0%, transparent 60%),
                     linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.3) 100%);
  --gradient-page-bg: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--primary) / 0.02) 25%, hsl(var(--background)) 50%, hsl(var(--secondary) / 0.03) 75%, hsl(var(--background)) 100%);
  --gradient-section-light: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted) / 0.3) 50%, hsl(var(--background)) 100%);
  --gradient-section-accent: linear-gradient(135deg, hsl(var(--accent) / 0.1) 0%, hsl(var(--background)) 50%, hsl(var(--primary) / 0.05) 100%);
  --gradient-glass: linear-gradient(135deg, hsl(var(--background) / 0.8) 0%, hsl(var(--background) / 0.4) 100%);
  --gradient-glass-card: linear-gradient(135deg, hsl(var(--card) / 0.9) 0%, hsl(var(--card) / 0.6) 100%);

  /* Enhanced Hero Gradient for better visibility */
  --gradient-hero-enhanced: linear-gradient(135deg, hsl(var(--primary) / 0.15) 0%, hsl(var(--background)) 30%, hsl(var(--secondary) / 0.12) 70%, hsl(var(--background)) 100%),
                           radial-gradient(circle at 25% 25%, hsl(var(--primary) / 0.2) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, hsl(var(--secondary) / 0.18) 0%, transparent 50%),
                           radial-gradient(circle at 50% 50%, hsl(var(--accent) / 0.1) 0%, transparent 70%);

  /* Enhanced Shadow System */
  --shadow-soft: 0 2px 8px -2px hsl(var(--primary) / 0.08);
  --shadow-medium: 0 4px 16px -4px hsl(var(--primary) / 0.12);
  --shadow-strong: 0 8px 32px -8px hsl(var(--primary) / 0.16);
  --shadow-glow: 0 0 20px hsl(var(--secondary) / 0.25);
  --shadow-glow-hover: 0 0 30px hsl(var(--secondary) / 0.35);
  --shadow-glass: 0 8px 32px hsl(var(--primary) / 0.1), 0 2px 8px hsl(var(--primary) / 0.08);
  --shadow-floating: 0 20px 40px -12px hsl(var(--primary) / 0.15);
  --shadow-inner: inset 0 2px 4px hsl(var(--primary) / 0.06);
  --background: 0 0% 100%;
  --foreground: 158 15% 15%;
  --sidebar: 158 10% 98%;
  --sidebar-foreground: 158 15% 15%;
  --sidebar-primary: 158 64% 25%;
  --sidebar-primary-foreground: 45 25% 97%;
  --sidebar-accent: 158 25% 85%;
  --sidebar-accent-foreground: 158 64% 20%;
  --sidebar-border: 158 15% 90%;
  --sidebar-ring: 158 64% 25%;
}



body {
  background: var(--gradient-page-bg);
  color: hsl(var(--foreground));
  font-family: var(--font-sans);
  line-height: 1.6;
  letter-spacing: -0.01em;
  min-height: 100vh;
}

/* Enhanced Typography System */
.text-brand-primary {
  color: hsl(var(--primary));
}

.text-brand-secondary {
  color: hsl(var(--secondary));
}

.text-brand-accent {
  color: hsl(var(--accent-foreground));
}

/* Luxury Typography Classes */
.text-luxury {
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.heading-luxury {
  /* Fallback color for browsers that don't support gradient text */
  color: hsl(var(--primary));
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* Fallback for browsers that don't support background-clip: text */
@supports not (-webkit-background-clip: text) {
  .heading-luxury {
    color: hsl(var(--primary));
    background: none;
  }
}

/* Enhanced Heading Styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.3;
  color: hsl(var(--foreground));
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Premium Text Effects */
.text-shimmer {
  background: linear-gradient(
    90deg,
    hsl(var(--primary)) 0%,
    hsl(var(--secondary)) 50%,
    hsl(var(--primary)) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: var(--shadow-glow);
  }
  50% {
    box-shadow: var(--shadow-glow-hover);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.6;
  }
  66% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 0.4;
  }
}

@keyframes subtle-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

/* ===== UTILITY CLASSES ===== */

/* Custom animations not available in Tailwind */
.animate-shimmer {
  background: linear-gradient(90deg, transparent, hsl(var(--muted) / 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-gradient-shift {
  background: linear-gradient(-45deg, hsl(var(--primary) / 0.1), hsl(var(--secondary) / 0.1), hsl(var(--accent) / 0.1), hsl(var(--primary) / 0.1));
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.animate-morph {
  animation: morph 8s ease-in-out infinite;
}

.animate-particle-float {
  animation: particle-float 6s ease-in-out infinite;
}

.animate-subtle-pulse {
  animation: subtle-pulse 4s ease-in-out infinite;
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-shimmer,
  .animate-gradient-shift,
  .animate-morph,
  .animate-particle-float,
  .animate-subtle-pulse,
  .animate-float,
  .animate-pulse-glow {
    animation: none;
  }

  .hover-lift:hover,
  .hover-scale:hover {
    transform: none;
  }

  .transition-all-smooth,
  .transition-transform-smooth {
    transition: none;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Reduce particle count on mobile */
  .animate-particle-float:nth-child(n+4) {
    display: none;
  }

  /* Simplify gradients on mobile for better performance */
  .bg-mesh-1,
  .bg-mesh-2 {
    background: var(--gradient-section-light);
  }

  /* Reduce blur effects on mobile */
  .bg-glass,
  .bg-glass-card {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  /* Optimize shadows for mobile */
  .shadow-floating {
    box-shadow: var(--shadow-strong);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-glass,
  .bg-glass-card {
    background: hsl(var(--card));
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }

  .bg-mesh-1,
  .bg-mesh-2 {
    background: hsl(var(--background));
  }
}

/* Enhanced transitions with consistent timing */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform-smooth {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-strong);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow-hover);
}

.hover-glow-hover:hover {
  box-shadow: var(--shadow-glow-hover);
}

.shadow-strong {
  box-shadow: var(--shadow-strong);
}

.bg-gradient-luxury {
  background: var(--gradient-luxury);
}

.bg-gradient-hero-enhanced {
  background: var(--gradient-hero-enhanced);
}

/* Advanced Background Utilities */
.bg-mesh-1 {
  background: var(--gradient-mesh-1);
}

.bg-mesh-2 {
  background: var(--gradient-mesh-2);
}

.bg-page {
  background: var(--gradient-page-bg);
}

.bg-section-light {
  background: var(--gradient-section-light);
}

.bg-section-accent {
  background: var(--gradient-section-accent);
}

.bg-glass {
  background: var(--gradient-glass);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.bg-glass-card {
  background: var(--gradient-glass-card);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Enhanced Background Patterns */
.bg-pattern-dots {
  background-image: radial-gradient(circle, hsl(var(--primary) / 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-grid {
  background-image:
    linear-gradient(hsl(var(--primary) / 0.05) 1px, transparent 1px),
    linear-gradient(90deg, hsl(var(--primary) / 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern-diagonal {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    hsl(var(--primary) / 0.03) 10px,
    hsl(var(--primary) / 0.03) 20px
  );
}

.hover-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* ===== LIQUID GLASS DESIGN SYSTEM ===== */
.liquid-glass-container {
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.liquid-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(234, 88, 12, 0.1) 0%,
    rgba(249, 115, 22, 0.1) 50%,
    rgba(251, 146, 60, 0.1) 100%
  );
  border-radius: 24px;
  z-index: -1;
}

.liquid-glass-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-icon-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.liquid-glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.liquid-glass-button-primary {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.3) 0%,
    hsl(var(--primary) / 0.3) 100%
  );
  color: white;
  box-shadow:
    0 4px 16px hsl(var(--primary) / 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button-primary::before {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.4) 0%,
    hsl(var(--primary) / 0.4) 100%
  );
  opacity: 0;
}

.liquid-glass-button-primary:hover::before {
  opacity: 1;
}

.liquid-glass-button-primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px hsl(var(--primary) / 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-button-secondary {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  color: white;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button-secondary::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
}

.liquid-glass-button-secondary:hover::before {
  opacity: 1;
}

.liquid-glass-button-secondary:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
  .liquid-glass-container {
    padding: 20px;
    border-radius: 20px;
    margin: 0 8px;
  }

  .liquid-glass-button {
    padding: 14px 18px;
    font-size: 15px;
    border-radius: 14px;
  }

  .liquid-glass-card {
    padding: 14px;
    border-radius: 14px;
  }

  .liquid-glass-icon {
    width: 44px;
    height: 44px;
    border-radius: 14px;
  }

  .liquid-glass-icon-small {
    width: 32px;
    height: 32px;
    border-radius: 10px;
  }
}

/* Enhanced Focus States for Accessibility */
.liquid-glass-button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Smooth Backdrop Animation */
@keyframes backdropFadeIn {
  from {
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0);
  }
  to {
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.2);
  }
}

@keyframes slideUpFadeIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Performance Optimizations - Only apply will-change when needed */
.liquid-glass-container:hover,
.liquid-glass-button:hover,
.liquid-glass-card:hover {
  will-change: transform;
}

/* Mobile Touch Targets */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* ===== MOBILE RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
  /* Prevent mobile browser zoom on form inputs */
  input,
  textarea,
  select {
    font-size: 16px;
  }

  /* Enhanced touch targets */
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

.mobile-drawer-optimized {
  /* Enhanced mobile drawer behavior */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 640px) {
  .mobile-drawer-optimized {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh;
    border-radius: 0;
  }
}

.mobile-quantity-controls {
  /* Better spacing for mobile touch */
  gap: 0;
}

@media (max-width: 768px) {
  .mobile-quantity-controls {
    border-width: 2px;
    border-radius: 12px;
  }

  .mobile-quantity-controls button {
    border-radius: 10px;
  }
}

.mobile-add-to-cart {
  /* Enhanced mobile add to cart button */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 768px) {
  .mobile-add-to-cart {
    min-height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    padding: 12px 20px;
  }

  .mobile-add-to-cart:active {
    transform: scale(0.96);
  }
}

/* Mobile Cart Animations */
@media (max-width: 768px) {
  .cart-item-mobile {
    padding: 16px;
    border-radius: 16px;
    margin-bottom: 12px;
  }

  .cart-drawer-content {
    padding: 20px 16px;
  }

  .cart-summary-mobile {
    padding: 20px 16px;
    border-radius: 20px 20px 0 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}

/* Enhanced Mobile Scrolling */
@media (max-width: 768px) {
  .cart-scroll-area {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .cart-scroll-area::-webkit-scrollbar {
    display: none;
  }
}

/* Mobile-specific hover states (remove on touch devices) */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-\[1\.02\]:hover {
    transform: none;
  }

  .hover\:shadow-md:hover {
    box-shadow: none;
  }
}

/* Mobile Cart Item Enhancements */
@media (max-width: 768px) {
  .mobile-cart-item {
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .mobile-cart-item:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
  }
}

/* Safe Area Support for Mobile */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .pb-safe {
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
  }
}

/* Mobile Drawer Improvements */
@media (max-width: 640px) {
  .cart-drawer-content {
    padding: 16px;
  }

  .cart-summary-mobile {
    margin: 0 -16px;
    padding: 20px 16px;
    border-radius: 20px 20px 0 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  }
}

/* Improved Touch Feedback */
@media (max-width: 768px) {
  .touch-target:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
  }

  .mobile-add-to-cart:active {
    background-color: rgba(0, 0, 0, 0.9);
    transform: scale(0.96);
  }

  button:active {
    transition: all 0.1s ease-out;
  }
}

/* Mobile Cart Summary Enhancements */
@media (max-width: 768px) {
  .mobile-cart-summary {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .mobile-cart-summary .progress {
    height: 6px;
    border-radius: 3px;
  }

  .mobile-cart-summary .badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 8px;
  }
}

/* Enhanced Mobile Animations */
@media (max-width: 768px) {
  .animate-in {
    animation-duration: 0.4s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .slide-in-from-right-2 {
    animation-name: slideInFromRight;
  }

  .slide-in-from-bottom-2 {
    animation-name: slideInFromBottom;
  }

  @keyframes slideInFromRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInFromBottom {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Mobile Empty Cart Enhancements */
@media (max-width: 768px) {
  .mobile-empty-cart {
    padding: 32px 16px;
  }

  .mobile-empty-cart-icon {
    margin-bottom: 24px;
  }

  .mobile-empty-cart h3 {
    font-size: 1.5rem;
    margin-bottom: 12px;
  }

  .mobile-empty-cart p {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 32px;
  }

  .mobile-empty-cart .mobile-add-to-cart {
    min-height: 52px;
    font-size: 16px;
    font-weight: 600;
  }
}

.dark {
  --background: 158 25% 8%;
  --foreground: 45 25% 95%;
  --card: 158 20% 12%;
  --card-foreground: 45 25% 95%;
  --popover: 158 20% 12%;
  --popover-foreground: 45 25% 95%;
  --primary: 158 55% 35%;
  --primary-foreground: 45 25% 97%;
  --secondary: 45 75% 55%;
  --secondary-foreground: 158 25% 8%;
  --muted: 158 15% 18%;
  --muted-foreground: 158 8% 70%;
  --accent: 158 20% 20%;
  --accent-foreground: 45 25% 95%;
  --destructive: 0 62% 50%;
  --border: 158 15% 25%;
  --input: 158 15% 22%;
  --ring: 158 55% 35%;
  --chart-1: 158 55% 35%;
  --chart-2: 45 75% 55%;
  --chart-3: 158 25% 75%;
  --chart-4: 158 35% 45%;
  --chart-5: 45 65% 45%;
  --sidebar: 158 20% 12%;
  --sidebar-foreground: 45 25% 95%;
  --sidebar-primary: 158 55% 35%;
  --sidebar-primary-foreground: 45 25% 97%;
  --sidebar-accent: 158 20% 20%;
  --sidebar-accent-foreground: 45 25% 95%;
  --sidebar-border: 158 15% 25%;
  --sidebar-ring: 158 55% 35%;
}

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}
