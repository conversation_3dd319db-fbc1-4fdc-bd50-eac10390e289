"use client";

import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { HeroAnimatedBackground, SectionAnimatedBackground } from "@/components/ui/animated-background";
import { Coffee, Package, Gift, Star, ArrowRight, Truck } from "lucide-react";

export default function Home() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <HeroAnimatedBackground className="py-20 lg:py-32 border-b border-primary/10">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center animate-fade-in-up">
            <h1 className="text-4xl lg:text-6xl font-bold tracking-tight mb-6">
              <span className="text-foreground">
                {t('homepage.hero.title')}
              </span>
              <span className="heading-luxury block">
                {t('homepage.hero.subtitle')}
              </span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-fade-in-up" style={{animationDelay: '0.2s'}}>
              {t('homepage.hero.description')}
            </p>

            {/* Primary CTA - Coffee Box Builder */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-fade-in-up" style={{animationDelay: '0.4s'}}>
              <Button variant="luxury" size="lg" className="text-lg px-8 py-6 h-auto group" asChild>
                <Link href={`/${locale}/coffee-box-builder`}>
                  <Package className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform-smooth" />
                  {t('homepage.hero.ctaPrimary')}
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform-smooth" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-6 h-auto group" asChild>
                <Link href={`/${locale}/shop`}>
                  <span className="group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-primary/80 group-hover:bg-clip-text group-hover:text-transparent transition-all-smooth">
                    {t('homepage.hero.ctaSecondary')}
                  </span>
                </Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Truck className="h-4 w-4" />
                <span>{t('homepage.trustIndicators.freeShipping')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 fill-current" />
                <span>{t('homepage.trustIndicators.premiumQuality')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Gift className="h-4 w-4" />
                <span>{t('homepage.trustIndicators.gifts')}</span>
              </div>
            </div>
          </div>
        </div>
      </HeroAnimatedBackground>

      {/* Features Section */}
      <SectionAnimatedBackground className="py-20">

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="heading-luxury text-3xl lg:text-4xl font-bold mb-4">
              {t('homepage.features.title')}
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              {t('homepage.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card variant="glass" className="text-center group hover-glow animate-fade-in-up transition-all-smooth" style={{animationDelay: '0.1s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-luxury rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-strong group-hover:shadow-glow-hover">
                  <Package className="h-6 w-6 text-primary-foreground group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.coffeeBoxBuilder.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.coffeeBoxBuilder.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={`/${locale}/coffee-box-builder`}>{t('homepage.features.coffeeBoxBuilder.cta')}</Link>
                </Button>
              </CardContent>
            </Card>

            <Card variant="glass" className="text-center group hover-glow animate-fade-in-up transition-all-smooth" style={{animationDelay: '0.2s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-luxury rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-strong group-hover:shadow-glow-hover">
                  <Gift className="h-6 w-6 text-primary-foreground group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.bundles.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.bundles.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={`/${locale}/bundles`}>{t('homepage.features.bundles.cta')}</Link>
                </Button>
              </CardContent>
            </Card>

            <Card variant="glass" className="text-center group hover-glow animate-fade-in-up transition-all-smooth" style={{animationDelay: '0.3s'}}>
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-gradient-luxury rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform-smooth shadow-strong group-hover:shadow-glow-hover">
                  <Coffee className="h-6 w-6 text-primary-foreground group-hover:rotate-12 transition-transform-smooth" />
                </div>
                <CardTitle className="group-hover:text-primary transition-colors">{t('homepage.features.shop.title')}</CardTitle>
                <CardDescription>
                  {t('homepage.features.shop.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  className="text-foreground hover:bg-gradient-primary hover:text-foreground hover:border-primary"
                  asChild
                >
                  <Link href={`/${locale}/shop`}>{t('homepage.features.shop.cta')}</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </SectionAnimatedBackground>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-luxury text-primary-foreground relative overflow-hidden">
        {/* Enhanced animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-morph"></div>
          <div className="absolute bottom-10 right-1/4 w-32 h-32 bg-white/5 rounded-full animate-morph" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 bg-white/15 rounded-full animate-particle-float" style={{animationDelay: '4s'}}></div>
          <div className="absolute top-1/3 right-10 w-20 h-20 bg-white/8 rounded-full animate-subtle-pulse" style={{animationDelay: '1s'}}></div>
        </div>

        {/* Gradient mesh overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent"></div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4 animate-fade-in-up">
            {t('homepage.finalCta.title')}
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto animate-fade-in-up" style={{animationDelay: '0.1s'}}>
            {t('homepage.finalCta.description')}
          </p>
          <Button size="lg" variant="secondary" className="text-lg px-8 py-6 h-auto shadow-strong hover:shadow-glow-hover hover:scale-105 group animate-fade-in-up" style={{animationDelay: '0.2s'}} asChild>
            <Link href={`/${locale}/coffee-box-builder`}>
              <Package className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform-smooth" />
              {t('homepage.finalCta.cta')}
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
